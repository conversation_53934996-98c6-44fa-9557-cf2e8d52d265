{"name": "vue-admin", "private": true, "version": "0.0.0", "type": "module", "homepage": "https://huccct.github.io/vue-admin", "scripts": {"dev": "vite --open", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint src", "fix": "eslint src --fix", "format": "prettier --write \"./**/*.{html,vue,ts,js,json,md}\"", "lint:eslint": "eslint src/**/*.{ts,vue} --cache --fix", "lint:style": "stylelint src/**/*.{css,scss,vue} --cache --fix", "prepare": "husky install", "commitlint": "commitlint --config commitlint.config.cjs -e -V", "preinstall": "node ./scripts/preinstall.js", "build:test": "vue-tsc && vite build --mode test", "build:pro": "vue-tsc && vite build --mode production", "commit": "git add . && git-cz"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.4.0", "echarts": "^5.4.2", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.3.4", "lodash": "^4.17.21", "moment": "^2.29.4", "pinia": "^2.1.3", "vue": "^3.2.47", "vue-router": "^4.2.1"}, "devDependencies": {"@babel/core": "^7.21.8", "@babel/eslint-parser": "^7.21.8", "@commitlint/cli": "^17.6.3", "@commitlint/config-conventional": "^17.6.3", "@types/nprogress": "^0.2.0", "@typescript-eslint/eslint-plugin": "^5.59.6", "@typescript-eslint/parser": "^5.59.6", "@vitejs/plugin-vue": "^4.1.0", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.13.0", "husky": "^8.0.0", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "postcss": "^8.4.23", "postcss-html": "^1.5.0", "postcss-scss": "^4.0.6", "prettier": "^2.8.8", "qs": "^6.11.2", "sass": "^1.62.1", "sass-loader": "^13.2.2", "stylelint": "^15.6.2", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^4.0.0", "stylelint-config-recommended-scss": "^11.0.0", "stylelint-config-standard": "^33.0.0", "stylelint-config-standard-scss": "^9.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-order": "^6.0.3", "stylelint-scss": "^5.0.0", "typescript": "^5.0.2", "unplugin-auto-import": "^0.16.0", "unplugin-element-plus": "^0.7.1", "unplugin-vue-components": "^0.24.1", "unplugin-vue-define-options": "^1.3.5", "vite": "^4.3.2", "vite-plugin-mock": "^2.9.6", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-tsc": "^1.4.2"}}