<h1 align="center">
    Vue-Admin
</h1>
<p align="center">
    <a href="https://github.com/vuejs/vue">
      <img src="https://img.shields.io/badge/vue-3.2.47-brightgreen" alt="vue">
    </a>
    <a href="https://github.com/ElemeFE/element">
      <img src="https://img.shields.io/badge/element--plus-2.3.4-brightgreen" alt="element-plus">
    </a>
    <a href="#">
        <img src="https://img.shields.io/github/stars/huccct/vue-admin">
    </a>
    <a href="#">
        <img src="https://img.shields.io/github/license/huccct/vue-admin">
    </a>
</p>

## 使用仓库相关命令

### 安装 pnpm

```
npm i pnpm -g
```

### 安装所有依赖

```
pnpm install
```

### 启动本地测试项目

```
pnpm run dev
```

### 打包

```
pnpm run build
```
