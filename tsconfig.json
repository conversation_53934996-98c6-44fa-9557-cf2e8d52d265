{"compilerOptions": {"baseUrl": "./", "paths": {"@/*": ["src/*"]}, "types": ["vite/client", "unplugin-vue-define-options/macros-global", "element-plus/global"], "target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ESNext", "DOM", "DOM.Iterable"], "skipLibCheck": true, "moduleResolution": "node", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "noEmit": true, "jsx": "preserve", "sourceMap": true, "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "auto-imports.d.ts"], "references": [{"path": "./tsconfig.node.json"}]}